package dex_user

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/nats-io/nats.go"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	natsClient "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/nats"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/agent_referral"
)

// SubscriptionInterface defines the interface for NATS subscriptions
type SubscriptionInterface interface {
	IsValid() bool
	Unsubscribe() error
}

// SubscriptionWrapper wraps nats.Subscription to implement SubscriptionInterface
type SubscriptionWrapper struct {
	*nats.Subscription
}

func (sw *SubscriptionWrapper) IsValid() bool {
	return sw.Subscription != nil && sw.Subscription.IsValid()
}

func (sw *SubscriptionWrapper) Unsubscribe() error {
	if sw.Subscription != nil {
		return sw.Subscription.Unsubscribe()
	}
	return nil
}

// DexUserSubscriberService handles NATS subscription for dex user events
type DexUserSubscriberService struct {
	natsClient           natsClient.Subscriber
	agentReferralService *agent_referral.AgentReferralService
	subscriptions        map[string]SubscriptionInterface // Track active subscriptions
	mu                   sync.RWMutex                     // Protect subscriptions map
}

// NewDexUserSubscriberService creates a new DexUserSubscriberService
func NewDexUserSubscriberService(natsClient natsClient.Subscriber, agentReferralService *agent_referral.AgentReferralService) *DexUserSubscriberService {
	return &DexUserSubscriberService{
		natsClient:           natsClient,
		agentReferralService: agentReferralService,
		subscriptions:        make(map[string]SubscriptionInterface),
	}
}

// Start begins listening to NATS streams for dex user events
func (s *DexUserSubscriberService) Start(ctx context.Context) error {
	global.GVA_LOG.Info("Starting DexUserSubscriberService")

	// Create or ensure the dex_user stream exists
	_, err := s.natsClient.AddStream(&nats.StreamConfig{
		Name:     natsClient.DexUserStream,
		Subjects: []string{natsClient.DexUserSubject},
		Storage:  nats.FileStorage,
	})
	if err != nil {
		return fmt.Errorf("failed to create dex_user stream: %w", err)
	}

	// Start dex user subscriber
	if err := s.startDexUserSubscriber(ctx); err != nil {
		return fmt.Errorf("failed to start dex user subscriber: %w", err)
	}

	global.GVA_LOG.Info("DexUserSubscriberService started successfully")
	return nil
}

// Stop gracefully stops all subscriptions
func (s *DexUserSubscriberService) Stop() {
	global.GVA_LOG.Info("Stopping DexUserSubscriberService")

	s.mu.Lock()
	defer s.mu.Unlock()

	for subject, sub := range s.subscriptions {
		if sub.IsValid() {
			if err := sub.Unsubscribe(); err != nil {
				global.GVA_LOG.Error("Failed to unsubscribe",
					zap.String("subject", subject),
					zap.Error(err))
			} else {
				global.GVA_LOG.Info("Successfully unsubscribed",
					zap.String("subject", subject))
			}
		}
		delete(s.subscriptions, subject)
	}

	global.GVA_LOG.Info("DexUserSubscriberService stopped")
}

// startDexUserSubscriber starts the dex user event subscriber
func (s *DexUserSubscriberService) startDexUserSubscriber(ctx context.Context) error {
	subject := natsClient.DexUserSubject
	consumerName := natsClient.AgentSyncUserConsumer

	global.GVA_LOG.Info("Starting dex user subscriber",
		zap.String("subject", subject),
		zap.String("consumer", consumerName))

	// Check if we already have an active subscription for this subject
	s.mu.RLock()
	if existingSub, exists := s.subscriptions[subject]; exists {
		s.mu.RUnlock()
		if existingSub.IsValid() {
			global.GVA_LOG.Info("Reusing existing subscription for dex user events",
				zap.String("subject", subject))
			return nil
		}
		// Remove invalid subscription
		s.mu.Lock()
		delete(s.subscriptions, subject)
		s.mu.Unlock()
	} else {
		s.mu.RUnlock()
	}

	// Try to create or reuse existing consumer
	sub, err := s.createOrReuseConsumer(ctx, subject, consumerName, func(msg *nats.Msg) {
		s.handleDexUserMessage(ctx, msg)
	})
	if err != nil {
		return fmt.Errorf("failed to create dex user subscriber: %w", err)
	}

	// Store the subscription
	s.mu.Lock()
	s.subscriptions[subject] = &SubscriptionWrapper{sub}
	s.mu.Unlock()

	// Handle graceful shutdown
	go func() {
		<-ctx.Done()
		s.cleanupSubscription(subject, "dex user")
	}()

	global.GVA_LOG.Info("Successfully started dex user subscriber",
		zap.String("subject", subject),
		zap.String("consumer", consumerName))

	return nil
}

// createOrReuseConsumer creates a new consumer or reuses an existing one
func (s *DexUserSubscriberService) createOrReuseConsumer(_ context.Context, subject, consumerName string, handler nats.MsgHandler) (*nats.Subscription, error) {
	streamName := natsClient.DexUserStream

	// Check if consumer already exists
	consumerInfo, err := s.natsClient.ConsumerInfo(streamName, consumerName)
	if err == nil && consumerInfo != nil {
		global.GVA_LOG.Info("Consumer already exists, attempting to bind to it",
			zap.String("consumer", consumerName),
			zap.String("subject", subject),
			zap.String("stream", streamName))

		// Try to bind to the existing consumer
		sub, err := s.natsClient.SubscribeJS(subject, handler,
			nats.Bind(streamName, consumerName),
			nats.ManualAck(),
		)

		if err != nil {
			// If binding fails, the consumer might be in an inconsistent state
			global.GVA_LOG.Warn("Failed to bind to existing consumer, attempting to delete and recreate",
				zap.String("consumer", consumerName),
				zap.String("subject", subject),
				zap.Error(err))

			// Try to delete the problematic consumer
			if deleteErr := s.natsClient.DeleteConsumer(streamName, consumerName); deleteErr != nil {
				global.GVA_LOG.Error("Failed to delete problematic consumer",
					zap.String("consumer", consumerName),
					zap.Error(deleteErr))
			} else {
				global.GVA_LOG.Info("Successfully deleted problematic consumer",
					zap.String("consumer", consumerName))
			}

			// Fall through to create a new consumer
		} else {
			global.GVA_LOG.Info("Successfully bound to existing consumer",
				zap.String("consumer", consumerName),
				zap.String("subject", subject))
			return sub, nil
		}
	}

	// Create a new consumer
	global.GVA_LOG.Info("Creating new consumer",
		zap.String("consumer", consumerName),
		zap.String("subject", subject),
		zap.String("stream", streamName))

	sub, err := s.natsClient.SubscribeJS(subject, handler,
		nats.Durable(consumerName),
		nats.ManualAck(),
		nats.BindStream(streamName),
		nats.AckWait(30*time.Second),
		nats.MaxDeliver(3), // Limit redelivery attempts
		nats.DeliverNew(),  // Only deliver new messages from now on
	)

	if err != nil {
		return nil, fmt.Errorf("failed to create consumer '%s': %w", consumerName, err)
	}

	global.GVA_LOG.Info("Successfully created new consumer",
		zap.String("consumer", consumerName),
		zap.String("subject", subject))

	return sub, nil
}

// cleanupSubscription cleans up a subscription
func (s *DexUserSubscriberService) cleanupSubscription(subject, description string) {
	s.mu.Lock()
	defer s.mu.Unlock()

	if sub, exists := s.subscriptions[subject]; exists {
		if sub.IsValid() {
			if err := sub.Unsubscribe(); err != nil {
				global.GVA_LOG.Error("Failed to unsubscribe from "+description,
					zap.String("subject", subject),
					zap.Error(err))
			} else {
				global.GVA_LOG.Info("Successfully unsubscribed from "+description,
					zap.String("subject", subject))
			}
		}
		delete(s.subscriptions, subject)
	}
}

// handleDexUserMessage processes dex user messages from NATS
func (s *DexUserSubscriberService) handleDexUserMessage(ctx context.Context, msg *nats.Msg) {
	// Log raw message for debugging
	dataPreview := string(msg.Data)
	if len(dataPreview) > 500 {
		dataPreview = dataPreview[:500] + "..."
	}
	global.GVA_LOG.Debug("Received raw dex user message",
		zap.String("subject", msg.Subject),
		zap.Int("data_size", len(msg.Data)),
		zap.String("data_preview", dataPreview))

	var userEvent natsClient.DexUserEvent
	if err := json.Unmarshal(msg.Data, &userEvent); err != nil {
		global.GVA_LOG.Error("Failed to unmarshal dex user message",
			zap.Error(err),
			zap.String("data", string(msg.Data)))
		msg.Ack()
		return
	}

	global.GVA_LOG.Info("Processing dex user event",
		zap.String("user_id", userEvent.UserID),
		zap.Int("wallet_count", len(userEvent.Wallets)))

	// Skip processing if essential fields are missing
	if userEvent.UserID == "" {
		global.GVA_LOG.Warn("Skipping user event with empty user_id")
		msg.Ack()
		return
	}

	// Process the user creation
	if err := s.processUserCreation(ctx, &userEvent); err != nil {
		global.GVA_LOG.Error("Failed to process user creation",
			zap.Error(err),
			zap.String("user_id", userEvent.UserID))
		// Don't ack the message so it can be retried
		return
	}

	global.GVA_LOG.Debug("Successfully processed dex user event",
		zap.String("user_id", userEvent.UserID))

	// Acknowledge the message
	msg.Ack()
}

// processUserCreation handles the creation of a new user from dex user event
func (s *DexUserSubscriberService) processUserCreation(ctx context.Context, userEvent *natsClient.DexUserEvent) error {
	userID, err := uuid.Parse(userEvent.UserID)
	if err != nil {
		return fmt.Errorf("invalid user ID format: %w", err)
	}

	// Use database transaction to handle race conditions
	return global.GVA_DB.Transaction(func(tx *gorm.DB) error {
		// Check if user already exists
		var existingUser model.User
		err := tx.Where("id = ?", userID).First(&existingUser).Error

		if err == nil {
			// User already exists, log and skip
			global.GVA_LOG.Info("User already exists, skipping creation",
				zap.String("user_id", userEvent.UserID))
			return nil
		}

		if err != gorm.ErrRecordNotFound {
			return fmt.Errorf("failed to check if user exists: %w", err)
		}

		// User doesn't exist, create new one with default Level 1 and Activity Cashback
		newUser := &model.User{
			ID:             userID,
			AgentLevelID:   1,   // Default to Level 1
			Email:          nil, // Will be set later if available
			InvitationCode: nil, // Will be set later if user creates one
		}

		if err := tx.Create(newUser).Error; err != nil {
			return fmt.Errorf("failed to create user: %w", err)
		}

		// Create referral snapshot for the new user
		snapshot := model.ReferralSnapshot{
			UserID:             userID,
			DirectCount:        0,
			TotalDownlineCount: 0,
		}

		if err := tx.Create(&snapshot).Error; err != nil {
			return fmt.Errorf("failed to create referral snapshot: %w", err)
		}

		// Create user wallets if provided
		for _, wallet := range userEvent.Wallets {
			if wallet.WalletAddress == "" || wallet.Chain == "" {
				continue // Skip invalid wallets
			}

			// Parse chain type
			chainType, err := s.parseChainType(wallet.Chain)
			if err != nil {
				global.GVA_LOG.Warn("Unsupported chain type, skipping wallet",
					zap.String("chain", wallet.Chain),
					zap.String("wallet_address", wallet.WalletAddress))
				continue
			}

			// Parse UUIDs if provided
			var walletID, walletAccountID *uuid.UUID
			if wallet.WalletID != "" {
				if id, err := uuid.Parse(wallet.WalletID); err == nil {
					walletID = &id
				}
			}
			if wallet.WalletAccountID != "" {
				if id, err := uuid.Parse(wallet.WalletAccountID); err == nil {
					walletAccountID = &id
				}
			}

			userWallet := &model.UserWallet{
				UserID:          userID,
				Chain:           chainType,
				WalletAddress:   wallet.WalletAddress,
				WalletID:        walletID,
				WalletAccountID: walletAccountID,
			}

			if err := tx.Create(userWallet).Error; err != nil {
				// Log error but don't fail the entire transaction for wallet creation
				global.GVA_LOG.Error("Failed to create user wallet",
					zap.Error(err),
					zap.String("user_id", userEvent.UserID),
					zap.String("wallet_address", wallet.WalletAddress),
					zap.String("chain", wallet.Chain))
			}
		}

		global.GVA_LOG.Info("Successfully created new user from dex user event",
			zap.String("user_id", userEvent.UserID),
			zap.Int("wallet_count", len(userEvent.Wallets)))

		return nil
	})
}

// parseChainType converts string chain to model.ChainType
func (s *DexUserSubscriberService) parseChainType(chain string) (model.ChainType, error) {
	switch chain {
	case "EVM":
		return model.ChainEvm, nil
	case "SOLANA":
		return model.ChainSolana, nil
	case "TRON":
		return model.ChainTron, nil
	case "ARB":
		return model.ChainArb, nil
	default:
		return "", fmt.Errorf("unsupported chain type: %s", chain)
	}
}
